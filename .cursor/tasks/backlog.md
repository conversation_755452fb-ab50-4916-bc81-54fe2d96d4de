# Task Backlog

## iOS Serial Number Barcode Scanner POC

**Status**: Active Sprint
**Priority**: High
**Created**: 2024-12-19

### Overview
Implement an iOS POC application that can identify and decode a specific barcode associated with "Serial No." text on labels, even when multiple barcodes are present.

### Success Criteria
- ≥80% accuracy in identifying "Serial No." text on sample labels
- ≥80% accuracy in identifying and decoding the correct associated barcode
- Processing time within 2-3 seconds for clear images
- Core functionality demonstrated on iOS device

### Key Requirements
- Native iOS app using Swift
- On-device processing with Vision framework
- Camera input and photo library support
- Spatial association logic for text-barcode pairing
- Visual feedback with highlighting
- Support for common barcode formats (Code 128, QR, EAN, UPC)

### Technical Stack
- iOS 15+
- Swift
- Vision framework (VNRecognizeTextRequest, VNDetectBarcodesRequest)
- AVFoundation for camera
- SwiftUI for UI

### Notes
- Focus on POC functionality over polished UI
- All processing must be on-device
- Target spatial relationship: barcode to the right of "Serial No." text
