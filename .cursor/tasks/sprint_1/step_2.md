# Step 2: Camera Integration and Permissions

**Status**: pending
**Priority**: high
**Estimated Time**: 45 minutes

## Objective
Implement camera functionality with proper permission handling and live preview.

## Tasks
- [ ] Create `PermissionManager.swift` for handling camera/photo permissions
- [ ] Implement `CameraViewController.swift` with AVCaptureSession
- [ ] Create camera preview view with SwiftUI integration
- [ ] Add permission request flow
- [ ] Test camera preview on device/simulator

## Deliverables
- `PermissionManager.swift` - Permission handling logic
- `CameraViewController.swift` - Camera capture and preview
- `CameraView.swift` - SwiftUI wrapper for camera
- Updated `ContentView.swift` with camera integration

## Acceptance Criteria
- App requests camera permission on first launch
- Camera preview displays correctly
- Proper error handling for permission denied
- Camera session starts and stops properly
- No memory leaks in camera handling

## Technical Requirements
- Use AVCaptureSession for camera access
- Implement proper session lifecycle management
- Handle device orientation changes
- Support both front and back cameras (default to back)

## Notes
- Test on physical device for camera functionality
- Ensure proper cleanup of camera resources
- Handle edge cases like camera unavailable
