# Step 4: Spatial Association Logic

**Status**: pending
**Priority**: high
**Estimated Time**: 75 minutes

## Objective
Implement the core logic to associate barcodes with "Serial No." text based on spatial proximity.

## Tasks
- [ ] Create `BarcodeAssociationEngine.swift` for spatial analysis
- [ ] Implement proximity calculation algorithm
- [ ] Add horizontal alignment detection
- [ ] Create scoring system for best barcode match
- [ ] Test association logic with various layouts

## Deliverables
- `BarcodeAssociationEngine.swift` - Core association logic
- `SpatialAnalyzer.swift` - Geometric calculations
- `AssociationResult.swift` - Result model with confidence scores
- Unit tests for association logic

## Acceptance Criteria
- Correctly identifies barcode to the right of "Serial No." text
- Handles multiple barcodes and selects the best match
- Provides confidence scores for associations
- Works with various label layouts within tolerance
- Rejects associations when confidence is too low

## Technical Requirements
- Calculate Euclidean distance between text and barcode centers
- Prioritize horizontal alignment (same Y-coordinate ±tolerance)
- Prefer barcodes to the right of text (positive X offset)
- Implement weighted scoring: distance + alignment + direction
- Set minimum confidence threshold for valid associations

## Algorithm Details
1. Find all "Serial No." text instances
2. For each text, calculate distances to all barcodes
3. Apply spatial filters (horizontal alignment, right-side preference)
4. Score remaining candidates using weighted formula
5. Select highest scoring association above threshold

## Notes
- Test with edge cases: overlapping elements, rotated text
- Consider label orientation and camera angle variations
- Tune parameters based on sample label analysis
