// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		FD3232922DF0CB4E007C2EF6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD32327C2DF0CB4D007C2EF6 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = FD3232832DF0CB4D007C2EF6;
			remoteInfo = SerialBarcodeScanner;
		};
		FD32329C2DF0CB4E007C2EF6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD32327C2DF0CB4D007C2EF6 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = FD3232832DF0CB4D007C2EF6;
			remoteInfo = SerialBarcodeScanner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		FD3232842DF0CB4D007C2EF6 /* SerialBarcodeScanner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SerialBarcodeScanner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FD3232912DF0CB4E007C2EF6 /* SerialBarcodeScannerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SerialBarcodeScannerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		FD32329B2DF0CB4E007C2EF6 /* SerialBarcodeScannerUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SerialBarcodeScannerUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		FD3232862DF0CB4D007C2EF6 /* SerialBarcodeScanner */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SerialBarcodeScanner;
			sourceTree = "<group>";
		};
		FD3232942DF0CB4E007C2EF6 /* SerialBarcodeScannerTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SerialBarcodeScannerTests;
			sourceTree = "<group>";
		};
		FD32329E2DF0CB4E007C2EF6 /* SerialBarcodeScannerUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SerialBarcodeScannerUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		FD3232812DF0CB4D007C2EF6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD32328E2DF0CB4E007C2EF6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD3232982DF0CB4E007C2EF6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		FD32327B2DF0CB4D007C2EF6 = {
			isa = PBXGroup;
			children = (
				FD3232862DF0CB4D007C2EF6 /* SerialBarcodeScanner */,
				FD3232942DF0CB4E007C2EF6 /* SerialBarcodeScannerTests */,
				FD32329E2DF0CB4E007C2EF6 /* SerialBarcodeScannerUITests */,
				FD3232852DF0CB4D007C2EF6 /* Products */,
			);
			sourceTree = "<group>";
		};
		FD3232852DF0CB4D007C2EF6 /* Products */ = {
			isa = PBXGroup;
			children = (
				FD3232842DF0CB4D007C2EF6 /* SerialBarcodeScanner.app */,
				FD3232912DF0CB4E007C2EF6 /* SerialBarcodeScannerTests.xctest */,
				FD32329B2DF0CB4E007C2EF6 /* SerialBarcodeScannerUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		FD3232832DF0CB4D007C2EF6 /* SerialBarcodeScanner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FD3232A52DF0CB4E007C2EF6 /* Build configuration list for PBXNativeTarget "SerialBarcodeScanner" */;
			buildPhases = (
				FD3232802DF0CB4D007C2EF6 /* Sources */,
				FD3232812DF0CB4D007C2EF6 /* Frameworks */,
				FD3232822DF0CB4D007C2EF6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				FD3232862DF0CB4D007C2EF6 /* SerialBarcodeScanner */,
			);
			name = SerialBarcodeScanner;
			packageProductDependencies = (
			);
			productName = SerialBarcodeScanner;
			productReference = FD3232842DF0CB4D007C2EF6 /* SerialBarcodeScanner.app */;
			productType = "com.apple.product-type.application";
		};
		FD3232902DF0CB4E007C2EF6 /* SerialBarcodeScannerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FD3232A82DF0CB4E007C2EF6 /* Build configuration list for PBXNativeTarget "SerialBarcodeScannerTests" */;
			buildPhases = (
				FD32328D2DF0CB4E007C2EF6 /* Sources */,
				FD32328E2DF0CB4E007C2EF6 /* Frameworks */,
				FD32328F2DF0CB4E007C2EF6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				FD3232932DF0CB4E007C2EF6 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				FD3232942DF0CB4E007C2EF6 /* SerialBarcodeScannerTests */,
			);
			name = SerialBarcodeScannerTests;
			packageProductDependencies = (
			);
			productName = SerialBarcodeScannerTests;
			productReference = FD3232912DF0CB4E007C2EF6 /* SerialBarcodeScannerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		FD32329A2DF0CB4E007C2EF6 /* SerialBarcodeScannerUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FD3232AB2DF0CB4E007C2EF6 /* Build configuration list for PBXNativeTarget "SerialBarcodeScannerUITests" */;
			buildPhases = (
				FD3232972DF0CB4E007C2EF6 /* Sources */,
				FD3232982DF0CB4E007C2EF6 /* Frameworks */,
				FD3232992DF0CB4E007C2EF6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				FD32329D2DF0CB4E007C2EF6 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				FD32329E2DF0CB4E007C2EF6 /* SerialBarcodeScannerUITests */,
			);
			name = SerialBarcodeScannerUITests;
			packageProductDependencies = (
			);
			productName = SerialBarcodeScannerUITests;
			productReference = FD32329B2DF0CB4E007C2EF6 /* SerialBarcodeScannerUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		FD32327C2DF0CB4D007C2EF6 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					FD3232832DF0CB4D007C2EF6 = {
						CreatedOnToolsVersion = 16.3;
					};
					FD3232902DF0CB4E007C2EF6 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = FD3232832DF0CB4D007C2EF6;
					};
					FD32329A2DF0CB4E007C2EF6 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = FD3232832DF0CB4D007C2EF6;
					};
				};
			};
			buildConfigurationList = FD32327F2DF0CB4D007C2EF6 /* Build configuration list for PBXProject "SerialBarcodeScanner" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = FD32327B2DF0CB4D007C2EF6;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = FD3232852DF0CB4D007C2EF6 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				FD3232832DF0CB4D007C2EF6 /* SerialBarcodeScanner */,
				FD3232902DF0CB4E007C2EF6 /* SerialBarcodeScannerTests */,
				FD32329A2DF0CB4E007C2EF6 /* SerialBarcodeScannerUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		FD3232822DF0CB4D007C2EF6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD32328F2DF0CB4E007C2EF6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD3232992DF0CB4E007C2EF6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		FD3232802DF0CB4D007C2EF6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD32328D2DF0CB4E007C2EF6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD3232972DF0CB4E007C2EF6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		FD3232932DF0CB4E007C2EF6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = FD3232832DF0CB4D007C2EF6 /* SerialBarcodeScanner */;
			targetProxy = FD3232922DF0CB4E007C2EF6 /* PBXContainerItemProxy */;
		};
		FD32329D2DF0CB4E007C2EF6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = FD3232832DF0CB4D007C2EF6 /* SerialBarcodeScanner */;
			targetProxy = FD32329C2DF0CB4E007C2EF6 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		FD3232A32DF0CB4E007C2EF6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 63M98WD275;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		FD3232A42DF0CB4E007C2EF6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 63M98WD275;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		FD3232A62DF0CB4E007C2EF6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 63M98WD275;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to scan barcodes and detect serial numbers on labels.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "This app needs photo library access to process images containing barcodes and serial numbers.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.longmaba.SerialBarcodeScanner;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		FD3232A72DF0CB4E007C2EF6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 63M98WD275;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to scan barcodes and detect serial numbers on labels.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "This app needs photo library access to process images containing barcodes and serial numbers.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.longmaba.SerialBarcodeScanner;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		FD3232A92DF0CB4E007C2EF6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 63M98WD275;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.longmaba.SerialBarcodeScannerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SerialBarcodeScanner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SerialBarcodeScanner";
			};
			name = Debug;
		};
		FD3232AA2DF0CB4E007C2EF6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 63M98WD275;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.longmaba.SerialBarcodeScannerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SerialBarcodeScanner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SerialBarcodeScanner";
			};
			name = Release;
		};
		FD3232AC2DF0CB4E007C2EF6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 63M98WD275;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.longmaba.SerialBarcodeScannerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = SerialBarcodeScanner;
			};
			name = Debug;
		};
		FD3232AD2DF0CB4E007C2EF6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 63M98WD275;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.longmaba.SerialBarcodeScannerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = SerialBarcodeScanner;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		FD32327F2DF0CB4D007C2EF6 /* Build configuration list for PBXProject "SerialBarcodeScanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FD3232A32DF0CB4E007C2EF6 /* Debug */,
				FD3232A42DF0CB4E007C2EF6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FD3232A52DF0CB4E007C2EF6 /* Build configuration list for PBXNativeTarget "SerialBarcodeScanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FD3232A62DF0CB4E007C2EF6 /* Debug */,
				FD3232A72DF0CB4E007C2EF6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FD3232A82DF0CB4E007C2EF6 /* Build configuration list for PBXNativeTarget "SerialBarcodeScannerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FD3232A92DF0CB4E007C2EF6 /* Debug */,
				FD3232AA2DF0CB4E007C2EF6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FD3232AB2DF0CB4E007C2EF6 /* Build configuration list for PBXNativeTarget "SerialBarcodeScannerUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FD3232AC2DF0CB4E007C2EF6 /* Debug */,
				FD3232AD2DF0CB4E007C2EF6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = FD32327C2DF0CB4D007C2EF6 /* Project object */;
}
