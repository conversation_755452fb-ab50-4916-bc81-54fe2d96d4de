Product Requirements Document: iOS Serial Number Barcode Scanner POC
Version: 1.0
Date: June 5, 2025
Author: Gemini AI
Status: Draft

1. Introduction
This document outlines the requirements for a Proof of Concept (POC) iOS application designed to identify and decode a specific barcode on a label. The primary challenge is to distinguish and process only the barcode associated with the "Serial No." text, even when multiple barcodes are present on the label, as illustrated in the provided sample image. This POC will leverage on-device machine learning capabilities, specifically Apple's Vision framework or Google's ML Kit, to achieve this.

2. Goals
The primary goals of this POC are:

Feasibility Assessment: Determine the technical feasibility of accurately identifying and decoding a specific barcode based on its proximity to a predefined text string ("Serial No.") using an iOS device's camera.

Accuracy Evaluation: Assess the accuracy of barcode detection, text recognition, and the logic for associating the correct barcode with the "Serial No." text under various conditions (e.g., different angles, lighting, label quality).

Performance Benchmark: Get a preliminary understanding of the processing speed and resource consumption on an iOS device.

Inform Future Development: Gather insights to inform the design and development of a potential production-level application.

3. Target User
For this POC, the target user is primarily the internal development and product team. The application will serve as a technical demonstration rather than a consumer-facing product at this stage.

4. Scope
4.1. In Scope
iOS Application: Develop a native iOS application.

Camera Input: Utilize the device's camera to capture live video feed or still images of labels.

Image from Library (Optional but Recommended): Allow users to select an image from their photo library for processing. This aids in repeatable testing.

Text Recognition: Implement text recognition to find the string "Serial No." (or similar variations like "Serial Number", "S/N").

Barcode Detection: Implement barcode detection to identify all barcodes present in the camera's view or selected image. Supported barcode formats should include common types found on product labels (e.g., Code 128, QR Code, EAN, UPC).

Targeted Barcode Identification: Develop logic to identify the specific barcode spatially located next to (primarily to the right of and on a similar horizontal plane as) the "Serial No." text.

Barcode Decoding: Decode the identified target barcode.

Display Results: Display the decoded value of the target barcode on the screen.

Visual Feedback (Basic): Provide basic visual feedback, such as highlighting the detected "Serial No." text and the target barcode on the preview/image.

4.2. Out of Scope
User Authentication/Accounts: No user login or account management.

Data Storage/History: No saving of scan history or decoded data beyond the current session.

Advanced UI/UX: The user interface will be functional and minimal, focusing on the core task. No polished design is required for the POC.

Cloud Processing: All processing (text recognition, barcode scanning) will be done on-device.

Extensive Error Handling for Edge Cases: While basic error handling is expected (e.g., no barcode found), comprehensive handling of all possible edge cases is not required for the POC.

Localization/Internationalization: The application will be developed in English. Recognition will focus on English "Serial No." text.

Support for Multiple Label Layouts: The initial logic will be tuned based on the provided sample image layout. While some robustness is desired, handling drastically different label layouts is out of scope for the POC.

Batch Processing: The POC will focus on processing one label/image at a time.

5. Functional Requirements
ID

Requirement Description

Priority

FR1

The application MUST allow the user to point the device camera at a label.

High

FR2

The application SHOULD allow the user to select an image of a label from the device's photo library.

Medium

FR3

The application MUST perform on-device text recognition to locate the text "Serial No." (or its common variations).

High

FR4

The application MUST perform on-device barcode detection to identify all 1D and 2D barcodes in the view/image.

High

FR5

The application MUST implement logic to identify the barcode spatially adjacent (e.g., to the right) to the "Serial No." text.

High

FR6

The application MUST decode the identified target barcode.

High

FR7

The application MUST display the decoded value of the target barcode to the user.

High

FR8

The application SHOULD provide visual feedback by highlighting the detected "Serial No." text and the target barcode.

Medium

FR9

The application SHOULD display a message if "Serial No." text is not found.

Medium

FR10

The application SHOULD display a message if no barcode is found near the "Serial No." text.

Medium

6. Non-Functional Requirements
ID

Requirement Description

Priority

NFR1

Performance: The processing (text recognition, barcode detection, and association) should ideally complete within 2-3 seconds for a clear image.

Medium

NFR2

Accuracy: The system should correctly identify and decode the target barcode in at least 80% of test cases with good quality labels and lighting.

High

NFR3

Usability (POC Level): The application should be straightforward for the development team to operate for testing purposes.

Medium

NFR4

Technology: The POC MUST be developed as a native iOS application using Swift. It SHOULD utilize Apple's Vision framework or Google's ML Kit for on-device ML tasks.

High

7. Technical Considerations
Platform: iOS (specify target iOS version, e.g., iOS 15+)

Development Language: Swift

Core Technologies:

Text Recognition: Apple Vision Framework (VNRecognizeTextRequest) or ML Kit Text Recognition.

Barcode Scanning: Apple Vision Framework (VNDetectBarcodesRequest) or ML Kit Barcode Scanning.

Permissions: The app will require camera permission and potentially photo library access permission.

8. Success Criteria
The POC will be considered successful if:

The application can consistently (>=80% accuracy on test set) identify the "Serial No." text on sample labels.

The application can consistently (>=80% accuracy on test set) identify and decode the barcode located spatially next to the "Serial No." text, ignoring other barcodes.

The core functionality is demonstrated on an iOS device.

The development team gains a clear understanding of the capabilities and limitations of the chosen ML technology for this specific use case.

Processing time is within acceptable limits for a positive user experience in a potential future product.

9. Assumptions
The "Serial No." text and its associated barcode are generally in a predictable spatial relationship (e.g., barcode to the right of the text).

Labels are reasonably flat and well-lit during scanning.

Standard barcode symbologies are used on the labels.

The text "Serial No." is printed in a clearly legible font and size.

10. Open Questions / Future Considerations (Post-POC)
How robust is the solution to variations in label layout, fonts, and lighting conditions?

What is the performance impact on older iOS devices?

How can the accuracy be improved for challenging conditions (e.g., blurry images, low light, damaged labels)?

Could this be expanded to identify other text-barcode pairs?

What are the requirements for a production-ready version (UI/UX, error handling, integration, etc.)?

Explore the possibility of user-defined text fields to look for (e.g., "Part No.", "Product ID").

Consider alternative ML models or fine-tuning if accuracy is insufficient.