import Foundation
import Vision

/// Represents a detected barcode with its properties
struct BarcodeDetectionResult {
    let payloadString: String?
    let symbology: VNBarcodeSymbology
    let boundingBox: CGRect
    let confidence: Float
    let observation: VNBarcodeObservation
    
    init(observation: VNBarcodeObservation) {
        self.payloadString = observation.payloadStringValue
        self.symbology = observation.symbology
        self.boundingBox = observation.boundingBox
        self.confidence = observation.confidence
        self.observation = observation
    }
    
    /// Get the center point of the barcode bounding box
    var centerPoint: CGPoint {
        return CGPoint(
            x: boundingBox.midX,
            y: boundingBox.midY
        )
    }
    
    /// Human-readable symbology name
    var symbologyName: String {
        switch symbology {
        case .aztec:
            return "Aztec"
        case .code39:
            return "Code 39"
        case .code39Checksum:
            return "Code 39 Checksum"
        case .code39FullASCII:
            return "Code 39 Full ASCII"
        case .code39FullASCIIChecksum:
            return "Code 39 Full ASCII Checksum"
        case .code93:
            return "Code 93"
        case .code93i:
            return "Code 93i"
        case .code128:
            return "Code 128"
        case .dataMatrix:
            return "Data Matrix"
        case .ean8:
            return "EAN-8"
        case .ean13:
            return "EAN-13"
        case .i2of5:
            return "Interleaved 2 of 5"
        case .i2of5Checksum:
            return "Interleaved 2 of 5 Checksum"
        case .itf14:
            return "ITF-14"
        case .pdf417:
            return "PDF417"
        case .qr:
            return "QR Code"
        case .upce:
            return "UPC-E"
        default:
            return "Unknown"
        }
    }
    
    /// Check if this is a supported barcode type for serial numbers
    var isSupportedType: Bool {
        let supportedTypes: [VNBarcodeSymbology] = [
            .code128,
            .qr,
            .ean8,
            .ean13,
            .upce,
            .code39,
            .code93,
            .dataMatrix
        ]
        return supportedTypes.contains(symbology)
    }
}

extension BarcodeDetectionResult: Identifiable {
    var id: String {
        return "\(symbology.rawValue)_\(boundingBox.origin.x)_\(boundingBox.origin.y)"
    }
}

extension BarcodeDetectionResult: CustomStringConvertible {
    var description: String {
        let payload = payloadString ?? "nil"
        return "BarcodeDetectionResult(payload: '\(payload)', type: \(symbologyName), confidence: \(confidence), bounds: \(boundingBox))"
    }
}
