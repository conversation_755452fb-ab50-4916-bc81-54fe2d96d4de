import Foundation
import Vision

/// Represents a detected text element with its properties
struct TextDetectionResult {
    let text: String
    let boundingBox: CGRect
    let confidence: Float
    let observation: VNRecognizedTextObservation
    
    init(text: String, boundingBox: CGRect, confidence: Float, observation: VNRecognizedTextObservation) {
        self.text = text
        self.boundingBox = boundingBox
        self.confidence = confidence
        self.observation = observation
    }
    
    /// Check if this text matches serial number patterns
    var isSerialNumberText: Bool {
        let lowercaseText = text.lowercased()
        let serialPatterns = [
            "serial no",
            "serial number",
            "s/n",
            "serial:",
            "sn:",
            "ser no",
            "ser num"
        ]
        
        return serialPatterns.contains { pattern in
            lowercaseText.contains(pattern)
        }
    }
    
    /// Get the center point of the text bounding box
    var centerPoint: CGPoint {
        return CGPoint(
            x: boundingBox.midX,
            y: boundingBox.midY
        )
    }
}

extension TextDetectionResult: Identifiable {
    var id: String {
        return "\(text)_\(boundingBox.origin.x)_\(boundingBox.origin.y)"
    }
}

extension TextDetectionResult: CustomStringConvertible {
    var description: String {
        return "TextDetectionResult(text: '\(text)', confidence: \(confidence), bounds: \(boundingBox))"
    }
}
