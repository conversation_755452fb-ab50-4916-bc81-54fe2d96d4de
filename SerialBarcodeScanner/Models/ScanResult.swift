import Foundation

/// Represents the complete result of a scan operation
struct ScanResult {
    let textResults: [TextDetectionResult]
    let barcodeResults: [BarcodeDetectionResult]
    let associationResults: [AssociationResult]
    let processingTime: TimeInterval
    let imageSize: CGSize
    
    init(
        textResults: [TextDetectionResult] = [],
        barcodeResults: [BarcodeDetectionResult] = [],
        associationResults: [AssociationResult] = [],
        processingTime: TimeInterval = 0,
        imageSize: CGSize = .zero
    ) {
        self.textResults = textResults
        self.barcodeResults = barcodeResults
        self.associationResults = associationResults
        self.processingTime = processingTime
        self.imageSize = imageSize
    }
    
    /// Get the best association result (highest confidence)
    var bestAssociation: AssociationResult? {
        return associationResults.max { $0.confidence < $1.confidence }
    }
    
    /// Get all serial number text detections
    var serialNumberTexts: [TextDetectionResult] {
        return textResults.filter { $0.isSerialNumberText }
    }
    
    /// Get all supported barcodes
    var supportedBarcodes: [BarcodeDetectionResult] {
        return barcodeResults.filter { $0.isSupportedType }
    }
    
    /// Check if scan was successful (found at least one valid association)
    var isSuccessful: Bool {
        return bestAssociation?.confidence ?? 0 > 0.5
    }
    
    /// Get summary statistics
    var summary: String {
        let textCount = textResults.count
        let barcodeCount = barcodeResults.count
        let associationCount = associationResults.count
        let bestConfidence = bestAssociation?.confidence ?? 0
        
        return """
        Scan Summary:
        - Text detections: \(textCount)
        - Barcode detections: \(barcodeCount)
        - Associations found: \(associationCount)
        - Best confidence: \(String(format: "%.2f", bestConfidence))
        - Processing time: \(String(format: "%.3f", processingTime))s
        """
    }
}

/// Represents an association between text and barcode
struct AssociationResult {
    let textResult: TextDetectionResult
    let barcodeResult: BarcodeDetectionResult
    let confidence: Float
    let distance: Float
    let spatialScore: Float
    
    init(
        textResult: TextDetectionResult,
        barcodeResult: BarcodeDetectionResult,
        confidence: Float,
        distance: Float,
        spatialScore: Float
    ) {
        self.textResult = textResult
        self.barcodeResult = barcodeResult
        self.confidence = confidence
        self.distance = distance
        self.spatialScore = spatialScore
    }
    
    /// Get the decoded barcode value
    var decodedValue: String {
        return barcodeResult.payloadString ?? "Unable to decode"
    }
    
    /// Get a human-readable description of the association
    var description: String {
        return "Found '\(textResult.text)' associated with \(barcodeResult.symbologyName) barcode: '\(decodedValue)' (confidence: \(String(format: "%.2f", confidence)))"
    }
}

extension AssociationResult: Identifiable {
    var id: String {
        return "\(textResult.id)_\(barcodeResult.id)"
    }
}
