import SwiftUI
import Foundation
import Vision

/// Overlay view for highlighting detected text and barcodes
struct OverlayView: View {
    let scanResult: ScanResult
    let imageSize: CGSize
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Highlight detected text
                ForEach(scanResult.textResults) { textResult in
                    RoundedRectangle(cornerRadius: 4)
                        .stroke(
                            textResult.isSerialNumberText ? Color.green : Color.yellow,
                            lineWidth: 2
                        )
                        .frame(
                            width: convertedRect(textResult.boundingBox, in: geometry.size).width,
                            height: convertedRect(textResult.boundingBox, in: geometry.size).height
                        )
                        .position(
                            x: convertedRect(textResult.boundingBox, in: geometry.size).midX,
                            y: convertedRect(textResult.boundingBox, in: geometry.size).midY
                        )
                        .animation(.easeInOut(duration: 0.3), value: scanResult.textResults.count)
                }
                
                // Highlight detected barcodes
                ForEach(scanResult.barcodeResults) { barcodeResult in
                    RoundedRectangle(cornerRadius: 4)
                        .stroke(
                            isAssociatedBarcode(barcodeResult) ? Color.blue : Color.orange,
                            lineWidth: 2
                        )
                        .frame(
                            width: convertedRect(barcodeResult.boundingBox, in: geometry.size).width,
                            height: convertedRect(barcodeResult.boundingBox, in: geometry.size).height
                        )
                        .position(
                            x: convertedRect(barcodeResult.boundingBox, in: geometry.size).midX,
                            y: convertedRect(barcodeResult.boundingBox, in: geometry.size).midY
                        )
                        .animation(.easeInOut(duration: 0.3), value: scanResult.barcodeResults.count)
                }
                
                // Show association lines
                ForEach(scanResult.associationResults) { association in
                    Path { path in
                        let textCenter = convertedPoint(association.textResult.centerPoint, in: geometry.size)
                        let barcodeCenter = convertedPoint(association.barcodeResult.centerPoint, in: geometry.size)
                        
                        path.move(to: textCenter)
                        path.addLine(to: barcodeCenter)
                    }
                    .stroke(Color.green, lineWidth: 2)
                    .opacity(0.7)
                    .animation(.easeInOut(duration: 0.3), value: scanResult.associationResults.count)
                }
                
                // Show confidence indicators
                ForEach(scanResult.associationResults) { association in
                    VStack(spacing: 4) {
                        Text("✓")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.green)
                        
                        Text("\(Int(association.confidence * 100))%")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.green)
                            .cornerRadius(4)
                    }
                    .position(
                        x: convertedPoint(association.barcodeResult.centerPoint, in: geometry.size).x,
                        y: convertedPoint(association.barcodeResult.centerPoint, in: geometry.size).y - 30
                    )
                    .animation(.easeInOut(duration: 0.3), value: scanResult.associationResults.count)
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    /// Convert Vision framework bounding box to SwiftUI coordinates
    private func convertedRect(_ boundingBox: CGRect, in viewSize: CGSize) -> CGRect {
        // Vision framework uses normalized coordinates (0-1) with origin at bottom-left
        // SwiftUI uses view coordinates with origin at top-left
        
        let scaleX = viewSize.width
        let scaleY = viewSize.height
        
        let x = boundingBox.origin.x * scaleX
        let y = (1 - boundingBox.origin.y - boundingBox.height) * scaleY
        let width = boundingBox.width * scaleX
        let height = boundingBox.height * scaleY
        
        return CGRect(x: x, y: y, width: width, height: height)
    }
    
    /// Convert Vision framework point to SwiftUI coordinates
    private func convertedPoint(_ point: CGPoint, in viewSize: CGSize) -> CGPoint {
        let scaleX = viewSize.width
        let scaleY = viewSize.height
        
        let x = point.x * scaleX
        let y = (1 - point.y) * scaleY
        
        return CGPoint(x: x, y: y)
    }
    
    /// Check if a barcode is associated with serial number text
    private func isAssociatedBarcode(_ barcode: BarcodeDetectionResult) -> Bool {
        return scanResult.associationResults.contains { association in
            association.barcodeResult.id == barcode.id
        }
    }
}

// MARK: - Preview

struct OverlayView_Previews: PreviewProvider {
    static var previews: some View {
        // Create sample data for preview
        let sampleTextResult = TextDetectionResult(
            text: "Serial No.",
            boundingBox: CGRect(x: 0.1, y: 0.5, width: 0.2, height: 0.1),
            confidence: 0.9,
            observation: VNRecognizedTextObservation()
        )
        
        let sampleBarcodeResult = BarcodeDetectionResult(
            observation: VNBarcodeObservation()
        )
        
        let sampleAssociation = AssociationResult(
            textResult: sampleTextResult,
            barcodeResult: sampleBarcodeResult,
            confidence: 0.85,
            distance: 0.1,
            spatialScore: 0.8
        )
        
        let sampleScanResult = ScanResult(
            textResults: [sampleTextResult],
            barcodeResults: [sampleBarcodeResult],
            associationResults: [sampleAssociation],
            processingTime: 1.2,
            imageSize: CGSize(width: 1920, height: 1080)
        )
        
        OverlayView(
            scanResult: sampleScanResult,
            imageSize: CGSize(width: 1920, height: 1080)
        )
        .frame(width: 300, height: 200)
        .background(Color.black.opacity(0.3))
    }
}

// MARK: - Supporting Extensions

extension OverlayView {
    
    /// Color scheme for different detection types
    enum HighlightColor {
        static let serialNumberText = Color.green
        static let regularText = Color.yellow
        static let associatedBarcode = Color.blue
        static let unassociatedBarcode = Color.orange
        static let associationLine = Color.green
        static let confidence = Color.green
    }
    
    /// Animation configuration
    enum Animation {
        static let duration: Double = 0.3
        static let highlightAnimation = SwiftUI.Animation.easeInOut(duration: duration)
    }
}
