import SwiftUI
import PhotosUI
import UIKit

/// Modern photo picker view using PhotosUI framework
struct PhotoPickerView: View {
    @Binding var scanResult: String
    @Binding var isProcessing: Bool
    @Environment(\.presentationMode) var presentationMode
    
    @StateObject private var visionProcessor = VisionProcessor()
    @StateObject private var permissionManager = PermissionManager()
    
    @State private var selectedItem: PhotosPickerItem?
    @State private var selectedImage: UIImage?
    @State private var showingPermissionAlert = false
    @State private var errorMessage: String?
    @State private var showingError = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                Text("Select Photo to Scan")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding()
                
                // Selected image preview
                if let selectedImage = selectedImage {
                    VStack(spacing: 12) {
                        Text("Selected Image")
                            .font(.headline)
                        
                        Image(uiImage: selectedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 300)
                            .cornerRadius(12)
                            .shadow(radius: 4)
                        
                        But<PERSON>("Process Image") {
                            processSelectedImage()
                        }
                        .buttonStyle(PrimaryButtonStyle())
                        .disabled(isProcessing)
                    }
                    .padding()
                } else {
                    // Photo picker
                    VStack(spacing: 16) {
                        Image(systemName: "photo.on.rectangle")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        
                        Text("Select an image containing a label with serial number and barcode")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                            .padding(.horizontal)
                        
                        PhotosPicker(
                            selection: $selectedItem,
                            matching: .images,
                            photoLibrary: .shared()
                        ) {
                            HStack {
                                Image(systemName: "photo.badge.plus")
                                Text("Choose Photo")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .padding(.horizontal)
                    }
                }
                
                Spacer()
                
                // Processing indicator
                if isProcessing {
                    VStack(spacing: 8) {
                        ProgressView()
                            .scaleEffect(1.2)
                        Text("Processing image...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
            }
            .navigationTitle("Photo Scanner")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
        .onChange(of: selectedItem) { newItem in
            loadSelectedImage(from: newItem)
        }
        .alert("Permission Required", isPresented: $showingPermissionAlert) {
            Button("Settings") {
                permissionManager.openAppSettings()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Photo library access is required to select images. Please enable it in Settings.")
        }
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage ?? "An unknown error occurred")
        }
        .onAppear {
            checkPhotoLibraryPermission()
        }
    }
    
    // MARK: - Private Methods
    
    private func checkPhotoLibraryPermission() {
        if !permissionManager.isPhotoLibraryPermissionGranted {
            permissionManager.requestPhotoLibraryPermission { granted in
                if !granted {
                    showingPermissionAlert = true
                }
            }
        }
    }
    
    private func loadSelectedImage(from item: PhotosPickerItem?) {
        guard let item = item else { return }
        
        item.loadTransferable(type: Data.self) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let data):
                    if let data = data, let image = UIImage(data: data) {
                        self.selectedImage = image
                    } else {
                        self.showError("Failed to load selected image")
                    }
                case .failure(let error):
                    self.showError("Error loading image: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func processSelectedImage() {
        guard let image = selectedImage else { return }
        
        isProcessing = true
        
        visionProcessor.processImage(image) { result in
            DispatchQueue.main.async {
                self.isProcessing = false
                
                switch result {
                case .success(let scanResult):
                    self.handleScanSuccess(scanResult)
                case .failure(let error):
                    self.showError("Processing failed: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func handleScanSuccess(_ result: ScanResult) {
        if let bestAssociation = result.bestAssociation {
            let resultText = """
            ✅ Serial Number Found!
            
            Text: "\(bestAssociation.textResult.text)"
            Barcode: \(bestAssociation.barcodeResult.symbologyName)
            Value: "\(bestAssociation.decodedValue)"
            Confidence: \(String(format: "%.1f%%", bestAssociation.confidence * 100))
            
            \(result.summary)
            """
            
            scanResult = resultText
        } else {
            let resultText = """
            ❌ No Serial Number Association Found
            
            \(result.summary)
            
            Detected Text: \(result.textResults.map { $0.text }.joined(separator: ", "))
            Detected Barcodes: \(result.barcodeResults.count)
            """
            
            scanResult = resultText
        }
        
        presentationMode.wrappedValue.dismiss()
    }
    
    private func showError(_ message: String) {
        errorMessage = message
        showingError = true
    }
}

// MARK: - Button Styles

struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .frame(maxWidth: .infinity)
            .padding()
            .background(configuration.isPressed ? Color.blue.opacity(0.8) : Color.blue)
            .foregroundColor(.white)
            .cornerRadius(10)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Preview

struct PhotoPickerView_Previews: PreviewProvider {
    static var previews: some View {
        PhotoPickerView(
            scanResult: .constant("No result yet"),
            isProcessing: .constant(false)
        )
    }
}
