import SwiftUI
import AVFoundation
import UIKit

/// Camera view with live preview and capture functionality
struct CameraView: View {
    @Binding var scanResult: String
    @Binding var isProcessing: Bool
    @Environment(\.presentationMode) var presentationMode

    @StateObject private var cameraManager = CameraManager()
    @StateObject private var visionProcessor = VisionProcessor()
    @StateObject private var permissionManager = PermissionManager()

    @State private var showingPermissionAlert = false
    @State private var errorMessage: String?
    @State private var showingError = false
    @State private var capturedImage: UIImage?
    @State private var showingOverlay = false
    @State private var overlayResults: ScanResult?
    @State private var continuousProcessingTimer: Timer?
    
    var body: some View {
        ZStack {
            // Camera preview
            CameraPreviewView(cameraManager: cameraManager)
                .ignoresSafeArea()
            
            // Overlay for detected elements
            if showingOverlay, let results = overlayResults {
                OverlayView(scanResult: results, imageSize: cameraManager.previewSize)
                    .allowsHitTesting(false)
            }
            
            // UI Controls
            VStack {
                // Top bar
                HStack {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                    .padding()
                    
                    Spacer()
                    
                    if isProcessing {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                            .padding()
                    }
                }
                
                Spacer()
                
                // Instructions
                VStack(spacing: 8) {
                    Text("Point camera at label with serial number")
                        .font(.headline)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        .background(Color.black.opacity(0.6))
                        .cornerRadius(8)
                    
                    if let results = overlayResults, results.isSuccessful {
                        Text("✅ Serial number detected!")
                            .font(.subheadline)
                            .foregroundColor(.green)
                            .fontWeight(.bold)
                            .padding(.horizontal)
                            .background(Color.black.opacity(0.6))
                            .cornerRadius(8)
                    }
                }
                .padding()
                
                // Capture button
                Button(action: captureImage) {
                    Circle()
                        .fill(Color.white)
                        .frame(width: 70, height: 70)
                        .overlay(
                            Circle()
                                .stroke(Color.black, lineWidth: 2)
                                .frame(width: 60, height: 60)
                        )
                }
                .disabled(isProcessing)
                .padding(.bottom, 50)
            }
        }
        .onAppear {
            setupCamera()
        }
        .onDisappear {
            cameraManager.stopSession()
            continuousProcessingTimer?.invalidate()
        }
        .alert("Camera Permission Required", isPresented: $showingPermissionAlert) {
            Button("Settings") {
                permissionManager.openAppSettings()
            }
            Button("Cancel", role: .cancel) {
                presentationMode.wrappedValue.dismiss()
            }
        } message: {
            Text("Camera access is required to scan barcodes. Please enable it in Settings.")
        }
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage ?? "An unknown error occurred")
        }
    }
    
    // MARK: - Private Methods
    
    private func setupCamera() {
        if permissionManager.isCameraPermissionGranted {
            startCamera()
        } else {
            permissionManager.requestCameraPermission { granted in
                if granted {
                    startCamera()
                } else {
                    showingPermissionAlert = true
                }
            }
        }
    }
    
    private func startCamera() {
        cameraManager.startSession { error in
            if let error = error {
                showError("Camera error: \(error.localizedDescription)")
            } else {
                // Start continuous processing for overlay
                startContinuousProcessing()
            }
        }
    }
    
    private func startContinuousProcessing() {
        // Process frames periodically for overlay feedback
        continuousProcessingTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            guard !isProcessing else { return }

            cameraManager.captureFrame { image in
                guard let image = image else { return }

                visionProcessor.processImage(image) { result in
                    DispatchQueue.main.async {
                        switch result {
                        case .success(let scanResult):
                            self.overlayResults = scanResult
                            self.showingOverlay = true
                        case .failure:
                            self.showingOverlay = false
                        }
                    }
                }
            }
        }
    }
    
    private func captureImage() {
        isProcessing = true
        
        cameraManager.capturePhoto { image in
            guard let image = image else {
                DispatchQueue.main.async {
                    self.isProcessing = false
                    self.showError("Failed to capture image")
                }
                return
            }
            
            self.capturedImage = image
            self.processImage(image)
        }
    }
    
    private func processImage(_ image: UIImage) {
        visionProcessor.processImage(image) { result in
            DispatchQueue.main.async {
                self.isProcessing = false
                
                switch result {
                case .success(let scanResult):
                    self.handleScanSuccess(scanResult)
                case .failure(let error):
                    self.showError("Processing failed: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func handleScanSuccess(_ result: ScanResult) {
        if let bestAssociation = result.bestAssociation {
            let resultText = """
            ✅ Serial Number Found!
            
            Text: "\(bestAssociation.textResult.text)"
            Barcode: \(bestAssociation.barcodeResult.symbologyName)
            Value: "\(bestAssociation.decodedValue)"
            Confidence: \(String(format: "%.1f%%", bestAssociation.confidence * 100))
            
            \(result.summary)
            """
            
            scanResult = resultText
        } else {
            let resultText = """
            ❌ No Serial Number Association Found
            
            \(result.summary)
            
            Detected Text: \(result.textResults.map { $0.text }.joined(separator: ", "))
            Detected Barcodes: \(result.barcodeResults.count)
            """
            
            scanResult = resultText
        }
        
        presentationMode.wrappedValue.dismiss()
    }
    
    private func showError(_ message: String) {
        errorMessage = message
        showingError = true
    }
}

// MARK: - Preview

struct CameraView_Previews: PreviewProvider {
    static var previews: some View {
        CameraView(
            scanResult: .constant("No result yet"),
            isProcessing: .constant(false)
        )
    }
}
