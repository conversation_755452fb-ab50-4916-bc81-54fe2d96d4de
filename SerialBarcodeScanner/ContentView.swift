import SwiftUI
import Vision
import AVFoundation
import Photos

struct ContentView: View {
    @State private var scanResult: String = "No scan result yet"
    @State private var isProcessing: Bool = false
    @State private var showingImagePicker: Bool = false
    @State private var showingCamera: Bool = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                Text("Serial Number Barcode Scanner")
                    .font(.title2)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                    .padding()
                
                // Status indicator
                if isProcessing {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Processing...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
                
                // Result display
                VStack(alignment: .leading, spacing: 8) {
                    Text("Scan Result:")
                        .font(.headline)
                    
                    Text(scanResult)
                        .font(.body)
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Action buttons
                VStack(spacing: 16) {
                    Button(action: {
                        showingCamera = true
                    }) {
                        HStack {
                            Image(systemName: "camera")
                            Text("Scan with Camera")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    
                    Button(action: {
                        showingImagePicker = true
                    }) {
                        HStack {
                            Image(systemName: "photo")
                            Text("Select from Photos")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 30)
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingCamera) {
            CameraView(scanResult: $scanResult, isProcessing: $isProcessing)
        }
        .sheet(isPresented: $showingImagePicker) {
            PhotoPickerView(scanResult: $scanResult, isProcessing: $isProcessing)
        }
    }
}

// Note: CameraView and PhotoPickerView are now implemented in separate files

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
