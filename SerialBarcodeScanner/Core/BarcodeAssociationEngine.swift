import Foundation
import CoreGraphics

/// Engine for associating barcodes with "Serial No." text based on spatial proximity
class BarcodeAssociationEngine {
    
    // MARK: - Configuration
    struct Configuration {
        let maxDistance: Float = 0.3 // Maximum normalized distance
        let horizontalAlignmentTolerance: Float = 0.1 // Y-coordinate tolerance
        let rightSidePreference: Float = 2.0 // Multiplier for right-side positioning
        let minimumConfidence: Float = 0.5 // Minimum confidence for valid association
        let distanceWeight: Float = 0.4
        let alignmentWeight: Float = 0.3
        let directionWeight: Float = 0.3
    }
    
    private let config = Configuration()
    private let spatialAnalyzer = SpatialAnalyzer()
    
    // MARK: - Public Methods
    
    /// Find associations between serial number text and barcodes
    /// - Parameters:
    ///   - textResults: Detected text results
    ///   - barcodeResults: Detected barcode results
    ///   - imageSize: Size of the processed image
    /// - Returns: Array of association results sorted by confidence
    func findAssociations(
        textResults: [TextDetectionResult],
        barcodeResults: [BarcodeDetectionResult],
        imageSize: CGSize
    ) -> [AssociationResult] {
        
        // Filter for serial number text
        let serialTexts = textResults.filter { $0.isSerialNumberText }
        
        // Filter for supported barcodes
        let supportedBarcodes = barcodeResults.filter { $0.isSupportedType }
        
        guard !serialTexts.isEmpty && !supportedBarcodes.isEmpty else {
            return []
        }
        
        var associations: [AssociationResult] = []
        
        // For each serial number text, find the best barcode association
        for textResult in serialTexts {
            if let bestAssociation = findBestAssociation(
                for: textResult,
                among: supportedBarcodes,
                imageSize: imageSize
            ) {
                associations.append(bestAssociation)
            }
        }
        
        // Sort by confidence (highest first) and remove duplicates
        associations = removeDuplicateAssociations(associations)
        associations.sort { $0.confidence > $1.confidence }
        
        return associations
    }
    
    // MARK: - Private Methods
    
    private func findBestAssociation(
        for textResult: TextDetectionResult,
        among barcodes: [BarcodeDetectionResult],
        imageSize: CGSize
    ) -> AssociationResult? {
        
        var bestAssociation: AssociationResult?
        var bestScore: Float = 0
        
        for barcode in barcodes {
            let score = calculateAssociationScore(
                text: textResult,
                barcode: barcode,
                imageSize: imageSize
            )
            
            if score > bestScore && score >= config.minimumConfidence {
                bestScore = score
                
                let distance = spatialAnalyzer.calculateDistance(
                    from: textResult.centerPoint,
                    to: barcode.centerPoint,
                    imageSize: imageSize
                )
                
                bestAssociation = AssociationResult(
                    textResult: textResult,
                    barcodeResult: barcode,
                    confidence: score,
                    distance: distance,
                    spatialScore: score
                )
            }
        }
        
        return bestAssociation
    }
    
    private func calculateAssociationScore(
        text: TextDetectionResult,
        barcode: BarcodeDetectionResult,
        imageSize: CGSize
    ) -> Float {
        
        // Calculate normalized distance
        let distance = spatialAnalyzer.calculateDistance(
            from: text.centerPoint,
            to: barcode.centerPoint,
            imageSize: imageSize
        )
        
        // Distance score (closer is better)
        let distanceScore = max(0, 1.0 - (distance / config.maxDistance))
        
        // Horizontal alignment score
        let alignmentScore = spatialAnalyzer.calculateHorizontalAlignment(
            text: text,
            barcode: barcode,
            tolerance: config.horizontalAlignmentTolerance
        )
        
        // Direction preference score (right side preferred)
        let directionScore = spatialAnalyzer.calculateDirectionScore(
            text: text,
            barcode: barcode,
            rightSidePreference: config.rightSidePreference
        )
        
        // Weighted combination
        let totalScore = (distanceScore * config.distanceWeight) +
                        (alignmentScore * config.alignmentWeight) +
                        (directionScore * config.directionWeight)
        
        // Apply confidence from text and barcode detection
        let detectionConfidence = (text.confidence + barcode.confidence) / 2.0
        
        return totalScore * detectionConfidence
    }
    
    private func removeDuplicateAssociations(_ associations: [AssociationResult]) -> [AssociationResult] {
        var uniqueAssociations: [AssociationResult] = []
        var usedBarcodes: Set<String> = []
        
        // Sort by confidence first
        let sortedAssociations = associations.sorted { $0.confidence > $1.confidence }
        
        for association in sortedAssociations {
            let barcodeId = association.barcodeResult.id
            
            if !usedBarcodes.contains(barcodeId) {
                uniqueAssociations.append(association)
                usedBarcodes.insert(barcodeId)
            }
        }
        
        return uniqueAssociations
    }
}

/// Helper class for spatial calculations
class SpatialAnalyzer {
    
    /// Calculate normalized distance between two points
    func calculateDistance(from point1: CGPoint, to point2: CGPoint, imageSize: CGSize) -> Float {
        let dx = Float(point2.x - point1.x) / Float(imageSize.width)
        let dy = Float(point2.y - point1.y) / Float(imageSize.height)
        return sqrt(dx * dx + dy * dy)
    }
    
    /// Calculate horizontal alignment score (0-1, where 1 is perfect alignment)
    func calculateHorizontalAlignment(
        text: TextDetectionResult,
        barcode: BarcodeDetectionResult,
        tolerance: Float
    ) -> Float {
        let yDifference = abs(Float(text.centerPoint.y - barcode.centerPoint.y))
        let normalizedDifference = yDifference / tolerance
        
        return max(0, 1.0 - normalizedDifference)
    }
    
    /// Calculate direction preference score (prefers barcodes to the right of text)
    func calculateDirectionScore(
        text: TextDetectionResult,
        barcode: BarcodeDetectionResult,
        rightSidePreference: Float
    ) -> Float {
        let xOffset = Float(barcode.centerPoint.x - text.centerPoint.x)
        
        if xOffset > 0 {
            // Barcode is to the right (preferred)
            return min(1.0, xOffset * rightSidePreference)
        } else {
            // Barcode is to the left (less preferred)
            return max(0, 0.5 + xOffset)
        }
    }
}
