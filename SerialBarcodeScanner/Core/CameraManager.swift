import Foundation
import SwiftUI
import AVFoundation
import UIKit

/// ObservableObject wrapper for camera functionality
class CameraManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isSessionRunning = false
    @Published var capturedImage: UIImage?
    @Published var latestFrame: UIImage?
    @Published var error: Error?
    
    // MARK: - Private Properties
    private var cameraViewController: CameraViewController?
    private let permissionManager = PermissionManager()
    
    // MARK: - Public Properties
    var previewSize: CGSize {
        return cameraViewController?.previewSize ?? .zero
    }
    
    // MARK: - Initialization
    
    init() {
        setupCameraViewController()
    }
    
    // MARK: - Public Methods
    
    /// Start the camera session
    /// - Parameter completion: Completion handler with error if any
    func startSession(completion: @escaping (Error?) -> Void) {
        guard permissionManager.isCameraPermissionGranted else {
            permissionManager.requestCameraPermission { [weak self] granted in
                if granted {
                    self?.startCameraSession(completion: completion)
                } else {
                    completion(CameraManagerError.permissionDenied)
                }
            }
            return
        }
        
        startCameraSession(completion: completion)
    }
    
    /// Stop the camera session
    func stopSession() {
        cameraViewController?.stopSession()
        isSessionRunning = false
    }
    
    /// Capture a photo
    /// - Parameter completion: Completion handler with captured image
    func capturePhoto(completion: @escaping (UIImage?) -> Void) {
        cameraViewController?.onPhotoCaptured = completion
        cameraViewController?.capturePhoto()
    }
    
    /// Capture current frame for processing
    /// - Parameter completion: Completion handler with current frame
    func captureFrame(completion: @escaping (UIImage?) -> Void) {
        // Return the latest frame if available
        completion(latestFrame)
    }
    
    /// Get the camera view controller for SwiftUI integration
    func getCameraViewController() -> CameraViewController? {
        return cameraViewController
    }
    
    // MARK: - Private Methods
    
    private func setupCameraViewController() {
        let controller = CameraViewController()
        
        // Set up callbacks
        controller.onFrameCaptured = { [weak self] image in
            DispatchQueue.main.async {
                self?.latestFrame = image
            }
        }
        
        controller.onPhotoCaptured = { [weak self] image in
            DispatchQueue.main.async {
                self?.capturedImage = image
            }
        }
        
        controller.onError = { [weak self] error in
            DispatchQueue.main.async {
                self?.error = error
                self?.isSessionRunning = false
            }
        }
        
        cameraViewController = controller
    }
    
    private func startCameraSession(completion: @escaping (Error?) -> Void) {
        guard let controller = cameraViewController else {
            completion(CameraManagerError.controllerNotAvailable)
            return
        }
        
        controller.startSession()
        isSessionRunning = true
        completion(nil)
    }
}

// MARK: - Camera Manager Errors

enum CameraManagerError: LocalizedError {
    case permissionDenied
    case controllerNotAvailable
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "Camera permission denied"
        case .controllerNotAvailable:
            return "Camera controller not available"
        }
    }
}

// MARK: - SwiftUI Integration

/// SwiftUI representable for camera preview
struct CameraPreviewView: UIViewControllerRepresentable {
    let cameraManager: CameraManager
    
    func makeUIViewController(context: Context) -> CameraViewController {
        return cameraManager.getCameraViewController() ?? CameraViewController()
    }
    
    func updateUIViewController(_ uiViewController: CameraViewController, context: Context) {
        // No updates needed for preview
    }
}

// MARK: - Preview Provider

struct CameraPreviewView_Previews: PreviewProvider {
    static var previews: some View {
        CameraPreviewView(cameraManager: CameraManager())
            .ignoresSafeArea()
    }
}
