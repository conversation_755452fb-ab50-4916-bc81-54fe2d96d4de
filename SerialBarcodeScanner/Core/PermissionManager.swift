import Foundation
import AVFoundation
import Photos
import UIKit

/// Manages camera and photo library permissions
class PermissionManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
    @Published var photoLibraryPermissionStatus: PHAuthorizationStatus = .notDetermined
    
    // MARK: - Initialization
    init() {
        updatePermissionStatuses()
    }
    
    // MARK: - Public Methods
    
    /// Request camera permission
    /// - Parameter completion: Completion handler with granted status
    func requestCameraPermission(completion: @escaping (Bool) -> Void) {
        AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
            DispatchQueue.main.async {
                self?.updateCameraPermissionStatus()
                completion(granted)
            }
        }
    }
    
    /// Request photo library permission
    /// - Parameter completion: Completion handler with granted status
    func requestPhotoLibraryPermission(completion: @escaping (Bool) -> Void) {
        if #available(iOS 14, *) {
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { [weak self] status in
                DispatchQueue.main.async {
                    self?.updatePhotoLibraryPermissionStatus()
                    let granted = status == .authorized || status == .limited
                    completion(granted)
                }
            }
        } else {
            PHPhotoLibrary.requestAuthorization { [weak self] status in
                DispatchQueue.main.async {
                    self?.updatePhotoLibraryPermissionStatus()
                    let granted = status == .authorized
                    completion(granted)
                }
            }
        }
    }
    
    /// Check if camera permission is granted
    var isCameraPermissionGranted: Bool {
        return cameraPermissionStatus == .authorized
    }
    
    /// Check if photo library permission is granted
    var isPhotoLibraryPermissionGranted: Bool {
        if #available(iOS 14, *) {
            return photoLibraryPermissionStatus == .authorized || photoLibraryPermissionStatus == .limited
        } else {
            return photoLibraryPermissionStatus == .authorized
        }
    }
    
    /// Open app settings for permission management
    func openAppSettings() {
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
            return
        }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    /// Get user-friendly permission status description
    func getPermissionStatusDescription(for permission: PermissionType) -> String {
        switch permission {
        case .camera:
            return getCameraPermissionDescription()
        case .photoLibrary:
            return getPhotoLibraryPermissionDescription()
        }
    }
    
    // MARK: - Private Methods
    
    private func updatePermissionStatuses() {
        updateCameraPermissionStatus()
        updatePhotoLibraryPermissionStatus()
    }
    
    private func updateCameraPermissionStatus() {
        cameraPermissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
    }
    
    private func updatePhotoLibraryPermissionStatus() {
        if #available(iOS 14, *) {
            photoLibraryPermissionStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        } else {
            photoLibraryPermissionStatus = PHPhotoLibrary.authorizationStatus()
        }
    }
    
    private func getCameraPermissionDescription() -> String {
        switch cameraPermissionStatus {
        case .notDetermined:
            return "Camera permission not requested"
        case .restricted:
            return "Camera access restricted"
        case .denied:
            return "Camera permission denied"
        case .authorized:
            return "Camera permission granted"
        @unknown default:
            return "Unknown camera permission status"
        }
    }
    
    private func getPhotoLibraryPermissionDescription() -> String {
        switch photoLibraryPermissionStatus {
        case .notDetermined:
            return "Photo library permission not requested"
        case .restricted:
            return "Photo library access restricted"
        case .denied:
            return "Photo library permission denied"
        case .authorized:
            return "Photo library permission granted"
        case .limited:
            return "Limited photo library access"
        @unknown default:
            return "Unknown photo library permission status"
        }
    }
}

// MARK: - Supporting Types

enum PermissionType {
    case camera
    case photoLibrary
}

// MARK: - Permission Alert Helper

extension PermissionManager {
    
    /// Create an alert for permission request
    func createPermissionAlert(
        for permission: PermissionType,
        onGrant: @escaping () -> Void,
        onDeny: @escaping () -> Void
    ) -> UIAlertController {
        
        let (title, message) = getAlertContent(for: permission)
        
        let alert = UIAlertController(
            title: title,
            message: message,
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Allow", style: .default) { _ in
            switch permission {
            case .camera:
                self.requestCameraPermission { granted in
                    if granted {
                        onGrant()
                    } else {
                        onDeny()
                    }
                }
            case .photoLibrary:
                self.requestPhotoLibraryPermission { granted in
                    if granted {
                        onGrant()
                    } else {
                        onDeny()
                    }
                }
            }
        })
        
        alert.addAction(UIAlertController.Action(title: "Cancel", style: .cancel) { _ in
            onDeny()
        })
        
        return alert
    }
    
    private func getAlertContent(for permission: PermissionType) -> (title: String, message: String) {
        switch permission {
        case .camera:
            return (
                title: "Camera Access Required",
                message: "This app needs camera access to scan barcodes and detect serial numbers on labels."
            )
        case .photoLibrary:
            return (
                title: "Photo Library Access Required",
                message: "This app needs photo library access to process images containing barcodes and serial numbers."
            )
        }
    }
}
