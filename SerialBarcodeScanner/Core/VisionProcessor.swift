import Foundation
import Vision
import UIKit
import CoreImage

/// Core processor for Vision framework operations
class VisionProcessor: ObservableObject {
    
    // MARK: - Error Types
    enum VisionError: LocalizedError {
        case invalidImage
        case textRecognitionFailed
        case barcodeDetectionFailed
        case noResultsFound
        case processingTimeout
        
        var errorDescription: String? {
            switch self {
            case .invalidImage:
                return "Invalid or corrupted image"
            case .textRecognitionFailed:
                return "Text recognition failed"
            case .barcodeDetectionFailed:
                return "Barcode detection failed"
            case .noResultsFound:
                return "No text or barcodes found in image"
            case .processingTimeout:
                return "Processing took too long"
            }
        }
    }
    
    // MARK: - Properties
    private let processingQueue = DispatchQueue(label: "vision.processing", qos: .userInitiated)
    private let associationEngine = BarcodeAssociationEngine()
    
    // MARK: - Public Methods
    
    /// Process an image for text and barcode detection
    /// - Parameters:
    ///   - image: The UIImage to process
    ///   - completion: Completion handler with result or error
    func processImage(_ image: UIImage, completion: @escaping (Result<ScanResult, Error>) -> Void) {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        processingQueue.async { [weak self] in
            guard let self = self else { return }
            
            guard let cgImage = image.cgImage else {
                DispatchQueue.main.async {
                    completion(.failure(VisionError.invalidImage))
                }
                return
            }
            
            let imageSize = CGSize(width: cgImage.width, height: cgImage.height)
            
            // Create request handler
            let requestHandler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            // Perform text and barcode detection concurrently
            let group = DispatchGroup()
            var textResults: [TextDetectionResult] = []
            var barcodeResults: [BarcodeDetectionResult] = []
            var errors: [Error] = []
            
            // Text recognition
            group.enter()
            self.performTextRecognition(requestHandler: requestHandler) { result in
                switch result {
                case .success(let results):
                    textResults = results
                case .failure(let error):
                    errors.append(error)
                }
                group.leave()
            }
            
            // Barcode detection
            group.enter()
            self.performBarcodeDetection(requestHandler: requestHandler) { result in
                switch result {
                case .success(let results):
                    barcodeResults = results
                case .failure(let error):
                    errors.append(error)
                }
                group.leave()
            }
            
            // Wait for both operations to complete
            group.notify(queue: self.processingQueue) {
                let processingTime = CFAbsoluteTimeGetCurrent() - startTime
                
                // Check for errors
                if !errors.isEmpty && textResults.isEmpty && barcodeResults.isEmpty {
                    DispatchQueue.main.async {
                        completion(.failure(errors.first ?? VisionError.noResultsFound))
                    }
                    return
                }
                
                // Perform spatial association
                let associationResults = self.associationEngine.findAssociations(
                    textResults: textResults,
                    barcodeResults: barcodeResults,
                    imageSize: imageSize
                )
                
                let scanResult = ScanResult(
                    textResults: textResults,
                    barcodeResults: barcodeResults,
                    associationResults: associationResults,
                    processingTime: processingTime,
                    imageSize: imageSize
                )
                
                DispatchQueue.main.async {
                    completion(.success(scanResult))
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func performTextRecognition(
        requestHandler: VNImageRequestHandler,
        completion: @escaping (Result<[TextDetectionResult], Error>) -> Void
    ) {
        let textRequest = VNRecognizeTextRequest { request, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let observations = request.results as? [VNRecognizedTextObservation] else {
                completion(.failure(VisionError.textRecognitionFailed))
                return
            }
            
            let results = self.processTextObservations(observations)
            completion(.success(results))
        }
        
        // Configure text recognition
        textRequest.recognitionLevel = .accurate
        textRequest.usesLanguageCorrection = true
        
        do {
            try requestHandler.perform([textRequest])
        } catch {
            completion(.failure(error))
        }
    }
    
    private func performBarcodeDetection(
        requestHandler: VNImageRequestHandler,
        completion: @escaping (Result<[BarcodeDetectionResult], Error>) -> Void
    ) {
        let barcodeRequest = VNDetectBarcodesRequest { request, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let observations = request.results as? [VNBarcodeObservation] else {
                completion(.failure(VisionError.barcodeDetectionFailed))
                return
            }
            
            let results = observations.map { BarcodeDetectionResult(observation: $0) }
            completion(.success(results))
        }
        
        // Configure barcode detection for common types
        barcodeRequest.symbologies = [
            .code128,
            .qr,
            .ean8,
            .ean13,
            .upce,
            .code39,
            .code93,
            .dataMatrix,
            .pdf417
        ]
        
        do {
            try requestHandler.perform([barcodeRequest])
        } catch {
            completion(.failure(error))
        }
    }
    
    private func processTextObservations(_ observations: [VNRecognizedTextObservation]) -> [TextDetectionResult] {
        var results: [TextDetectionResult] = []
        
        for observation in observations {
            guard let topCandidate = observation.topCandidates(1).first else { continue }
            
            let result = TextDetectionResult(
                text: topCandidate.string,
                boundingBox: observation.boundingBox,
                confidence: topCandidate.confidence,
                observation: observation
            )
            
            results.append(result)
        }
        
        return results
    }
}
