import UIKit
import AVFoundation
import SwiftUI

/// UIKit-based camera controller for AVCaptureSession management
class CameraViewController: UIViewController {
    
    // MARK: - Properties
    private var captureSession: AVCaptureSession?
    private var videoPreviewLayer: AVCaptureVideoPreviewLayer?
    private var photoOutput: AVCapturePhotoOutput?
    private var videoOutput: AVCaptureVideoDataOutput?
    
    private let sessionQueue = DispatchQueue(label: "camera.session.queue")
    
    // Callbacks
    var onFrameCaptured: ((UIImage?) -> Void)?
    var onPhotoCaptured: ((UIImage?) -> Void)?
    var onError: ((Error) -> Void)?
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupCamera()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        startSession()
    }
    
    override func viewWillDisappear(_ animated: <PERSON>ol) {
        super.viewWillDisappear(animated)
        stopSession()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        videoPreviewLayer?.frame = view.bounds
    }
    
    // MARK: - Camera Setup
    
    private func setupCamera() {
        sessionQueue.async { [weak self] in
            self?.configureCaptureSession()
        }
    }
    
    private func configureCaptureSession() {
        guard let captureDevice = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
            DispatchQueue.main.async {
                self.onError?(CameraError.deviceNotAvailable)
            }
            return
        }
        
        do {
            let captureSession = AVCaptureSession()
            captureSession.sessionPreset = .photo
            
            // Configure input
            let input = try AVCaptureDeviceInput(device: captureDevice)
            if captureSession.canAddInput(input) {
                captureSession.addInput(input)
            } else {
                throw CameraError.inputConfigurationFailed
            }
            
            // Configure photo output
            let photoOutput = AVCapturePhotoOutput()
            if captureSession.canAddOutput(photoOutput) {
                captureSession.addOutput(photoOutput)
                self.photoOutput = photoOutput
            } else {
                throw CameraError.outputConfigurationFailed
            }
            
            // Configure video output for frame capture
            let videoOutput = AVCaptureVideoDataOutput()
            videoOutput.setSampleBufferDelegate(self, queue: sessionQueue)
            if captureSession.canAddOutput(videoOutput) {
                captureSession.addOutput(videoOutput)
                self.videoOutput = videoOutput
            }
            
            self.captureSession = captureSession
            
            DispatchQueue.main.async {
                self.setupPreviewLayer()
            }
            
        } catch {
            DispatchQueue.main.async {
                self.onError?(error)
            }
        }
    }
    
    private func setupPreviewLayer() {
        guard let captureSession = captureSession else { return }
        
        let previewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        previewLayer.videoGravity = .resizeAspectFill
        previewLayer.frame = view.bounds
        
        view.layer.addSublayer(previewLayer)
        videoPreviewLayer = previewLayer
    }
    
    // MARK: - Session Management
    
    func startSession() {
        sessionQueue.async { [weak self] in
            guard let captureSession = self?.captureSession, !captureSession.isRunning else { return }
            captureSession.startRunning()
        }
    }
    
    func stopSession() {
        sessionQueue.async { [weak self] in
            guard let captureSession = self?.captureSession, captureSession.isRunning else { return }
            captureSession.stopRunning()
        }
    }
    
    // MARK: - Photo Capture
    
    func capturePhoto() {
        guard let photoOutput = photoOutput else {
            onError?(CameraError.photoOutputNotAvailable)
            return
        }
        
        let settings = AVCapturePhotoSettings()
        settings.flashMode = .auto
        
        photoOutput.capturePhoto(with: settings, delegate: self)
    }
    
    // MARK: - Frame Capture
    
    func captureCurrentFrame() {
        // Frame capture is handled automatically through video output delegate
        // The latest frame will be provided via onFrameCaptured callback
    }
    
    // MARK: - Configuration
    
    var previewSize: CGSize {
        return videoPreviewLayer?.bounds.size ?? .zero
    }
}

// MARK: - AVCapturePhotoCaptureDelegate

extension CameraViewController: AVCapturePhotoCaptureDelegate {
    
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        if let error = error {
            onError?(error)
            return
        }
        
        guard let imageData = photo.fileDataRepresentation(),
              let image = UIImage(data: imageData) else {
            onError?(CameraError.imageProcessingFailed)
            return
        }
        
        DispatchQueue.main.async {
            self.onPhotoCaptured?(image)
        }
    }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate

extension CameraViewController: AVCaptureVideoDataOutputSampleBufferDelegate {
    
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        guard let pixelBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) else { return }
        
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        let context = CIContext()
        
        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else { return }
        
        let image = UIImage(cgImage: cgImage)
        
        DispatchQueue.main.async {
            self.onFrameCaptured?(image)
        }
    }
}

// MARK: - Camera Errors

enum CameraError: LocalizedError {
    case deviceNotAvailable
    case inputConfigurationFailed
    case outputConfigurationFailed
    case photoOutputNotAvailable
    case imageProcessingFailed
    
    var errorDescription: String? {
        switch self {
        case .deviceNotAvailable:
            return "Camera device not available"
        case .inputConfigurationFailed:
            return "Failed to configure camera input"
        case .outputConfigurationFailed:
            return "Failed to configure camera output"
        case .photoOutputNotAvailable:
            return "Photo output not available"
        case .imageProcessingFailed:
            return "Failed to process captured image"
        }
    }
}
